// Super Admin Login Authentication
(function() {
    'use strict';

    // Super Admin Credentials (hardcoded as requested)
    const SUPER_ADMIN_CREDENTIALS = {
        email: '<EMAIL>',
        password: 'mallorca'
    };

    // Session management
    const SESSION_KEY = 'superAdminSession';
    const SESSION_DURATION = 8 * 60 * 60 * 1000; // 8 hours in milliseconds

    // DOM elements
    let form, emailInput, passwordInput, loginButton, loadingOverlay;
    let emailError, passwordError, generalError;

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeElements();
        setupEventListeners();
        checkExistingSession();
    });

    function initializeElements() {
        form = document.getElementById('super-admin-form');
        emailInput = document.getElementById('email');
        passwordInput = document.getElementById('password');
        loginButton = document.getElementById('login-button');
        loadingOverlay = document.getElementById('loading-overlay');
        emailError = document.getElementById('email-error');
        passwordError = document.getElementById('password-error');
        generalError = document.getElementById('general-error');
    }

    function setupEventListeners() {
        form.addEventListener('submit', handleLogin);
        
        // Clear errors when user starts typing
        emailInput.addEventListener('input', () => clearError('email'));
        passwordInput.addEventListener('input', () => clearError('password'));
        
        // Handle Enter key
        emailInput.addEventListener('keypress', handleEnterKey);
        passwordInput.addEventListener('keypress', handleEnterKey);
    }

    function handleEnterKey(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            handleLogin(event);
        }
    }

    function handleLogin(event) {
        event.preventDefault();
        
        const email = emailInput.value.trim();
        const password = passwordInput.value;

        // Clear previous errors
        clearAllErrors();

        // Validate inputs
        if (!validateInputs(email, password)) {
            return;
        }

        // Show loading state
        showLoading(true);

        // Simulate authentication delay for security
        setTimeout(() => {
            authenticateUser(email, password);
        }, 1000);
    }

    function validateInputs(email, password) {
        let isValid = true;

        if (!email) {
            showError('email', 'Email address is required');
            isValid = false;
        } else if (!isValidEmail(email)) {
            showError('email', 'Please enter a valid email address');
            isValid = false;
        }

        if (!password) {
            showError('password', 'Password is required');
            isValid = false;
        }

        return isValid;
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function authenticateUser(email, password) {
        // Check credentials
        if (email === SUPER_ADMIN_CREDENTIALS.email && password === SUPER_ADMIN_CREDENTIALS.password) {
            // Create session
            const session = {
                email: email,
                loginTime: Date.now(),
                expiresAt: Date.now() + SESSION_DURATION,
                sessionId: generateSessionId()
            };

            // Store session
            localStorage.setItem(SESSION_KEY, JSON.stringify(session));
            
            // Log access attempt (for audit purposes)
            logAccessAttempt(email, true);

            // Redirect to dashboard
            showLoading(false);
            window.location.href = 'super-admin-dashboard.html';
        } else {
            // Invalid credentials
            showLoading(false);
            showError('general', 'Invalid credentials. Access denied.');
            logAccessAttempt(email, false);
        }
    }

    function checkExistingSession() {
        const sessionData = localStorage.getItem(SESSION_KEY);
        
        if (sessionData) {
            try {
                const session = JSON.parse(sessionData);
                
                // Check if session is still valid
                if (session.expiresAt > Date.now()) {
                    // Valid session exists, redirect to dashboard
                    window.location.href = 'super-admin-dashboard.html';
                    return;
                }
            } catch (error) {
                console.error('Error parsing session data:', error);
            }
            
            // Clear invalid session
            localStorage.removeItem(SESSION_KEY);
        }
    }

    function generateSessionId() {
        return 'sa_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    function logAccessAttempt(email, success) {
        // Log access attempts for audit purposes
        const logEntry = {
            timestamp: new Date().toISOString(),
            email: email,
            success: success,
            userAgent: navigator.userAgent,
            ip: 'client-side', // Would be populated server-side in production
            sessionId: success ? generateSessionId() : null,
            attemptType: 'login'
        };

        // Store in localStorage for now (in production, this would be sent to server)
        const accessLogs = JSON.parse(localStorage.getItem('superAdminAccessLogs') || '[]');
        accessLogs.push(logEntry);

        // Keep only last 100 entries
        if (accessLogs.length > 100) {
            accessLogs.splice(0, accessLogs.length - 100);
        }

        localStorage.setItem('superAdminAccessLogs', JSON.stringify(accessLogs));

        // Also log to console for development (remove in production)
        console.log(`[AUDIT] Super Admin ${success ? 'Login Success' : 'Login Failed'}: ${email} at ${logEntry.timestamp}`);

        // In production, send to secure audit endpoint
        // sendAuditLog(logEntry);
    }

    function sendAuditLog(logEntry) {
        // This would send audit logs to a secure server endpoint
        // fetch('/api/audit/super-admin', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(logEntry)
        // });
    }

    function showError(field, message) {
        const errorElement = field === 'general' ? generalError : 
                           field === 'email' ? emailError : passwordError;
        
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    function clearError(field) {
        const errorElement = field === 'email' ? emailError : passwordError;
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    function clearAllErrors() {
        [emailError, passwordError, generalError].forEach(element => {
            if (element) {
                element.style.display = 'none';
            }
        });
    }

    function showLoading(show) {
        if (loadingOverlay) {
            loadingOverlay.style.display = show ? 'flex' : 'none';
        }
        
        if (loginButton) {
            loginButton.disabled = show;
            loginButton.textContent = show ? 'Authenticating...' : 'Access Dashboard';
        }
    }

    // Export session management functions for use in dashboard
    window.SuperAdminAuth = {
        isAuthenticated: function() {
            const sessionData = localStorage.getItem(SESSION_KEY);
            if (!sessionData) return false;
            
            try {
                const session = JSON.parse(sessionData);
                return session.expiresAt > Date.now();
            } catch (error) {
                return false;
            }
        },
        
        logout: function() {
            localStorage.removeItem(SESSION_KEY);
            window.location.href = 'super-admin-login.html';
        },
        
        getSession: function() {
            const sessionData = localStorage.getItem(SESSION_KEY);
            if (!sessionData) return null;
            
            try {
                const session = JSON.parse(sessionData);
                return session.expiresAt > Date.now() ? session : null;
            } catch (error) {
                return null;
            }
        },

        extendSession: function() {
            const session = this.getSession();
            if (session) {
                session.expiresAt = Date.now() + SESSION_DURATION;
                localStorage.setItem(SESSION_KEY, JSON.stringify(session));
            }
        }
    };

})();
